import { Controller, Post, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { WelcomeService } from './welcome.service';
import { AuthGuard } from '../common/guards/auth.guard';

@ApiTags('Welcome')
@Controller('welcome')
@UseGuards(AuthGuard)
export class WelcomeController {
  constructor(private readonly welcomeService: WelcomeService) {}

  @Post('test/:userId')
  @ApiOperation({ summary: 'Test welcome message functionality (for testing only)' })
  @ApiResponse({ status: 200, description: 'Welcome message sent successfully' })
  @ApiResponse({ status: 400, description: 'Failed to send welcome message' })
  async testWelcomeMessage(@Param('userId') userId: string) {
    const userIdNum = parseInt(userId, 10);
    if (isNaN(userIdNum)) {
      return { success: false, message: 'Invalid user ID' };
    }
    
    return this.welcomeService.sendTestWelcomeMessage(userIdNum);
  }
}
